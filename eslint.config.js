import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';
import react from 'eslint-plugin-react';
import jsxA11y from 'eslint-plugin-jsx-a11y';
import importPlugin from 'eslint-plugin-import';
import prettier from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier';

export default tseslint.config([
  // Global ignores
  {
    ignores: ['dist/**', 'build/**', 'vite.config.mjs']
  },

  // Main configuration for TypeScript and React files
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    ignores: ['eslint.config.js', '.eslintrc.js'],
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
      prettierConfig
    ],
    plugins: {
      react,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'jsx-a11y': jsxA11y,
      import: importPlugin,
      prettier
    },
    languageOptions: {
      ecmaVersion: 2021,
      globals: {
        ...globals.browser,
        ...globals.es2021,
        NEXT_PUBLIC_BACKEND_URL: 'readonly',
        REACT_APP_BACKEND_URL: 'readonly',
        COPYRIGHT_YEAR: 'readonly'
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        },
        projectService: true,
        tsconfigRootDir: import.meta.dirname
      }
    },
    settings: {
      react: {
        version: 'detect'
      },
      'import/resolver': {
        typescript: {
          alwaysTryTypes: true
        }
      }
    },
    rules: {
      // React recommended rules
      ...react.configs.recommended.rules,
      ...react.configs['jsx-runtime'].rules,

      // React Hooks rules
      ...reactHooks.configs.recommended.rules,

      // JSX A11y recommended rules
      ...jsxA11y.configs.recommended.rules,

      // Import plugin rules
      ...importPlugin.configs.recommended.rules,
      ...importPlugin.configs.typescript.rules,

      // React Refresh rules
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true }
      ],

      // Prettier rules
      'prettier/prettier': 'error',

      // Custom rules from original .eslintrc.js
      'import/no-extraneous-dependencies': 'off',
      'import/prefer-default-export': 'off',
      'jsx-a11y/label-has-for': 'off',
      'react/prop-types': 'off',
      'react/react-in-jsx-scope': 'off',
      'react/function-component-definition': [
        'error',
        {
          namedComponents: 'arrow-function',
          unnamedComponents: 'arrow-function'
        }
      ],
      'react/require-default-props': 'off',
      'react/jsx-props-no-spreading': 'off'
    }
  },

  // Configuration for config files
  {
    files: ['eslint.config.js', '.eslintrc.{js,cjs}'],
    languageOptions: {
      globals: {
        ...globals.node
      },
      parserOptions: {
        sourceType: 'script'
      }
    }
  }
]);
